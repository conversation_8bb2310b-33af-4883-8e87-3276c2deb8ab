<?php
namespace app\mbti\utils;
use app\common\service\AdReportService;

class BaiduReportUtils
{
    public static $url = "https://ocpc.baidu.com/ocpcapi/api/uploadConvertData";

    public static $zhanghuToken = [
        'bd1'=>"3QCLSgzTXDThJP84V53XnxZ5SLOdmLtf@WMImvlPj82jOiKcDzWCZKZhZy127sfWK",
        'bd2'=>"ZSiLKAVc3c3mxrBKxMRESuObjCQcLsJR@MryHcGMSEZHgaFx2i8VgMQuOkVi1qC9q",
        'bd4'=>"BOgAnZntko1gPKzGwVK82M6QajcrIww0@rG163HxkOXbYgMqcg2wXRfYQlmP4RvF9",
        'bd6'=>"y9BuzlfUFmGl0PkLBwVJRDCiWljzOF5r@9FrIvuInQdVcJvbwSuiiUXu2PH9euhvF",
        'bd11'=>"9pgOTw42mwsG4rdWgPvwIp5wZFq1UKOa@r3WvhzEki2IEXYNUlP9WUHgpmClzYtIN",
        'bd12'=>"h3k4n2O2Q5F4XIb8VXVpIWnL2iTy5KEM@KBkRhkX0oMwVqcNwnL1CSitJqPDeDs3Q",
    ];

    public static function send_report($data){
        if (isset(self::$zhanghuToken[$data['ad_acount']])){
            $token = self::$zhanghuToken[$data['ad_acount']];
        }else{
            $token = "";
        }
        $reqData = [
            'token' => $token,
            "conversionTypes"=>[
                [
                    'logidUrl'=>$data['request_url'],
                    'newType'=>3
                ]
            ]
        ];

       $res =  WangLuoUtils::http_post(self::$url,$reqData);
       if ($res['header']['status'] !== 0){
           $updateDate = [
               'state'=>3,
               'report_result'=>3,
               'marks'=>json_encode($res,JSON_UNESCAPED_UNICODE),
               'update_time'=>time()
           ];
       }else{
           $updateDate = [
               'state'=>3,
               'report_result'=>2,
               'marks'=>json_encode($res,JSON_UNESCAPED_UNICODE),
               'update_time'=>time()
           ];
       }
        AdReportService::update(['id' => $data['id']], $updateDate);

    }
}