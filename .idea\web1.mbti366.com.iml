<?xml version="1.0" encoding="UTF-8"?>
<module type="WEB_MODULE" version="4">
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/spec" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/tests" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/thinkphp/library/think" isTestSource="false" packagePrefix="think\" />
      <sourceFolder url="file://$MODULE_DIR$/addons/epay/library/hyperf/context/src" isTestSource="false" packagePrefix="Hyperf\Context\" />
      <sourceFolder url="file://$MODULE_DIR$/addons/epay/library/hyperf/context/tests" isTestSource="true" packagePrefix="HyperfTest\Context\" />
      <sourceFolder url="file://$MODULE_DIR$/addons/epay/library/hyperf/contract/src" isTestSource="false" packagePrefix="Hyperf\Contract\" />
      <sourceFolder url="file://$MODULE_DIR$/addons/epay/library/hyperf/engine/src" isTestSource="false" packagePrefix="Hyperf\Engine\" />
      <sourceFolder url="file://$MODULE_DIR$/addons/epay/library/hyperf/engine/tests" isTestSource="true" packagePrefix="HyperfTest\" />
      <sourceFolder url="file://$MODULE_DIR$/addons/epay/library/hyperf/macroable/src" isTestSource="false" packagePrefix="Hyperf\Macroable\" />
      <sourceFolder url="file://$MODULE_DIR$/addons/epay/library/hyperf/macroable/tests" isTestSource="true" packagePrefix="HyperfTest\Macroable\" />
      <sourceFolder url="file://$MODULE_DIR$/addons/epay/library/hyperf/pimple/src" isTestSource="false" packagePrefix="Hyperf\Pimple\" />
      <sourceFolder url="file://$MODULE_DIR$/addons/epay/library/hyperf/pimple/tests" isTestSource="true" packagePrefix="HyperfTest\" />
      <sourceFolder url="file://$MODULE_DIR$/addons/epay/library/hyperf/utils/src" isTestSource="false" packagePrefix="Hyperf\Utils\" />
      <sourceFolder url="file://$MODULE_DIR$/addons/epay/library/hyperf/utils/tests" isTestSource="true" packagePrefix="HyperfTest\Utils\" />
      <sourceFolder url="file://$MODULE_DIR$/thinkphp/tests/thinkphp" isTestSource="true" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>