<?php if (!defined('THINK_PATH')) exit(); /*a:2:{s:93:"F:\PHPCUSTOM\wwwroot\web1.mbti366.com\public/../application/mbti/view/default/index\main.html";i:1750150501;s:87:"F:\PHPCUSTOM\wwwroot\web1.mbti366.com\application\mbti\view\default\public\baidujs.html";i:1749283168;}*/ ?>
<!DOCTYPE html>
<html lang="zh-CN"
      style="--status-bar-height:0px; --top-window-height:0px; --window-left:0px; --window-right:0px; --window-margin:0px; font-size: 20.7px; --window-top:calc(var(--top-window-height) + 0px); --window-bottom:0px;">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>MBTI性格测试</title>
    <link rel="icon" type="image/x-icon">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1, maximum-scale=1, user-scalable=yes">
    <script src="/Public/layer/jquery.min.js"></script>
    <script src="/Public/layer/layer.js"></script>
    <link rel="stylesheet" href="/Public/mbit/index_files/index.a5c69d49.css">
    <style type="text/css">
            @font-face {
                font-family: YouSheBiaoTiHei;
                src: url(/Public/mbit/index_files/396385b48ad7cb2df0fea7b96a899414.eot);
                src: url(/Public/mbit/index_files/396385b48ad7cb2df0fea7b96a899414.woff2) format("woff2"),
                url(/Public/mbit/index_files/396385b48ad7cb2df0fea7b96a899414.woff) format("woff"),
                url(/Public/mbit/index_files/396385b48ad7cb2df0fea7b96a89941411.woff) format("truetype")
            }

            .footer-text[data-v-9cc6041a] {
                margin-top: 27px
            }

            .footer-text .text-title[data-v-9cc6041a] {
                margin-bottom: 11px;
                font-size: 15px;
                font-family: Alibaba PuHuiTi-Medium, Alibaba PuHuiTi;
                font-weight: 500;
                color: #745959;
                line-height: 30px
            }

            .footer-text .text-content[data-v-9cc6041a] {
                font-size: 12px;
                font-family: Alibaba PuHuiTi-Regular, Alibaba PuHuiTi;
                font-weight: 400;
                color: #524354;
                line-height: 24px
            }

            .customerService[data-v-9cc6041a] {
                position: fixed;
                right: 0;
                top: 71px;
                width: 30px;
                height: 80px;
                box-sizing: border-box;
                padding: 15px 9px;
                background: #fff;
                box-shadow: -2px -2px 4px 1px rgba(0, 0, 0, .05), 0 4px 4px 1px rgba(0, 0, 0, .05);
                border-radius: 5px 0 0 5px;
                z-index: 999;
                font-size: 13px;
                font-family: Roboto-Regular, Roboto;
                font-weight: 400;
                color: #5280ff;
                line-height: 15px
            }</style>
        <style type="text/css">@charset "UTF-8";

        body {
            background: #f7f8fa;
            font-family: PingFangSC-Regular, PingFang SC
        }

        .layout {
            font-family: PingFangSC-Regular, PingFang SC;
            margin: 0 auto;
            transition: all .5s ease 0s
        }

        /* pc样式
        */
        /* 小程序样式
        */
        @media screen and (min-width: 750px) {
            .layout {
                /*max-width: 750px;
        */
            }
        }

        @-webkit-keyframes shake { /* 水平抖动，核心代码 */
            10%, 90% {
                -webkit-transform: translate3d(-1px, 0, 0);
                transform: translate3d(-1px, 0, 0)
            }
            20%, 80% {
                -webkit-transform: translate3d(2px, 0, 0);
                transform: translate3d(2px, 0, 0)
            }
            30%, 70% {
                -webkit-transform: translate3d(-4px, 0, 0);
                transform: translate3d(-4px, 0, 0)
            }
            40%, 60% {
                -webkit-transform: translate3d(4px, 0, 0);
                transform: translate3d(4px, 0, 0)
            }
            50% {
                -webkit-transform: translate3d(-4px, 0, 0);
                transform: translate3d(-4px, 0, 0)
            }
        }

        @keyframes shake { /* 水平抖动，核心代码 */
            10%, 90% {
                -webkit-transform: translate3d(-1px, 0, 0);
                transform: translate3d(-1px, 0, 0)
            }
            20%, 80% {
                -webkit-transform: translate3d(2px, 0, 0);
                transform: translate3d(2px, 0, 0)
            }
            30%, 70% {
                -webkit-transform: translate3d(-4px, 0, 0);
                transform: translate3d(-4px, 0, 0)
            }
            40%, 60% {
                -webkit-transform: translate3d(4px, 0, 0);
                transform: translate3d(4px, 0, 0)
            }
            50% {
                -webkit-transform: translate3d(-4px, 0, 0);
                transform: translate3d(-4px, 0, 0)
            }
        }

        @-webkit-keyframes fire {
            0% {
                -webkit-transform: scaleX(1);
                transform: scaleX(1)
            }
            100% {
                -webkit-transform: scale3d(.7, .8, 1);
                transform: scale3d(.7, .8, 1)
            }
        }

        @keyframes fire {
            0% {
                -webkit-transform: scaleX(1);
                transform: scaleX(1)
            }
            100% {
                -webkit-transform: scale3d(.7, .8, 1);
                transform: scale3d(.7, .8, 1)
            }
        }

        @-webkit-keyframes shoot {
            0% {
                -webkit-transform: translateY(0);
                transform: translateY(0);
                opacity: 1
            }
            100% {
                -webkit-transform: translateY(-200px);
                transform: translateY(-200px);
                opacity: 0
            }
        }

        @keyframes shoot {
            0% {
                -webkit-transform: translateY(0);
                transform: translateY(0);
                opacity: 1
            }
            100% {
                -webkit-transform: translateY(-200px);
                transform: translateY(-200px);
                opacity: 0
            }
        }

        uni-slider {
            margin: 0 0 0 4px !important
        }

        .uni-slider-handle-wrapper {
            height: 11px !important
        }

        .u-input {
            height: 33px;
            padding: 0px 6px !important
        }

        .uni-input-input {
            text-align: center !important
        }

        .uni-slider-tap-area {
            padding: 3px 0 !important
        }

        @font-face {
            font-family: YouSheBiaoTiHei;
            src: url(/Public/mbit/index_files/396385b48ad7cb2df0fea7b96a899414.eot);
        url(/Public/mbit/index_files/396385b48ad7cb2df0fea7b96a899414.woff) format("woff"), url(/Public/mbit/index_files/396385b48ad7cb2df0fea7b96a899414.woff) format("truetype")
        }

        .footer-text {
            margin-top: 27px
        }

        .footer-text .text-title {
            margin-bottom: 11px;
            font-size: 15px;
            font-family: Alibaba PuHuiTi-Medium, Alibaba PuHuiTi;
            font-weight: 500;
            color: #745959;
            line-height: 30px
        }

        .footer-text .text-content {
            font-size: 12px;
            font-family: Alibaba PuHuiTi-Regular, Alibaba PuHuiTi;
            font-weight: 400;
            color: #524354;
            line-height: 24px
        }

        /*uView样式*/
        .u-line-1 {


            display: -webkit-box !important;
            overflow: hidden;
            text-overflow: ellipsis;
            word-break: break-all;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical !important
        }

        .u-line-2 {


            display: -webkit-box !important;
            overflow: hidden;
            text-overflow: ellipsis;
            word-break: break-all;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical !important
        }

        .u-line-3 {


            display: -webkit-box !important;
            overflow: hidden;
            text-overflow: ellipsis;
            word-break: break-all;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical !important
        }

        .u-line-4 {


            display: -webkit-box !important;
            overflow: hidden;
            text-overflow: ellipsis;
            word-break: break-all;
            -webkit-line-clamp: 4;
            -webkit-box-orient: vertical !important
        }

        .u-line-5 {


            display: -webkit-box !important;
            overflow: hidden;
            text-overflow: ellipsis;
            word-break: break-all;
            -webkit-line-clamp: 5;
            -webkit-box-orient: vertical !important
        }

        .u-border {
            border-width: .5px !important;
            border-color: #dadbde !important;
            border-style: solid
        }

        .u-border-top {
            border-top-width: .5px !important;
            border-color: #dadbde !important;
            border-top-style: solid
        }

        .u-border-left {
            border-left-width: .5px !important;
            border-color: #dadbde !important;
            border-left-style: solid
        }

        .u-border-right {
            border-right-width: .5px !important;
            border-color: #dadbde !important;
            border-right-style: solid
        }

        .u-border-bottom {
            border-bottom-width: .5px !important;
            border-color: #dadbde !important;
            border-bottom-style: solid
        }

        .u-border-top-bottom {
            border-top-width: .5px !important;
            border-bottom-width: .5px !important;
            border-color: #dadbde !important;
            border-top-style: solid;
            border-bottom-style: solid
        }

        .u-reset-button {
            padding: 0;
            background-color: initial;
            font-size: inherit;
            line-height: inherit;
            color: inherit;
        }

        .u-reset-button::after {
            border: none
        }

        .u-hover-class {
            opacity: .7
        }

        .u-primary-light {
            color: #ecf5ff
        }

        .u-warning-light {
            color: #fdf6ec
        }

        .u-success-light {
            color: #f5fff0
        }

        .u-error-light {
            color: #fef0f0
        }

        .u-info-light {
            color: #f4f4f5
        }

        .u-primary-light-bg {
            background-color: #ecf5ff
        }

        .u-warning-light-bg {
            background-color: #fdf6ec
        }

        .u-success-light-bg {
            background-color: #f5fff0
        }

        .u-error-light-bg {
            background-color: #fef0f0
        }

        .u-info-light-bg {
            background-color: #f4f4f5
        }

        .u-primary-dark {
            color: #398ade
        }

        .u-warning-dark {
            color: #f1a532
        }

        .u-success-dark {
            color: #53c21d
        }

        .u-error-dark {
            color: #e45656
        }

        .u-info-dark {
            color: #767a82
        }

        .u-primary-dark-bg {
            background-color: #398ade
        }

        .u-warning-dark-bg {
            background-color: #f1a532
        }

        .u-success-dark-bg {
            background-color: #53c21d
        }

        .u-error-dark-bg {
            background-color: #e45656
        }

        .u-info-dark-bg {
            background-color: #767a82
        }

        .u-primary-disabled {
            color: #9acafc
        }

        .u-warning-disabled {
            color: #f9d39b
        }

        .u-success-disabled {
            color: #a9e08f
        }

        .u-error-disabled {
            color: #f7b2b2
        }

        .u-info-disabled {
            color: #c4c6c9
        }

        .u-primary {
            color: #3c9cff
        }

        .u-warning {
            color: #f9ae3d
        }

        .u-success {
            color: #5ac725
        }

        .u-error {
            color: #f56c6c
        }

        .u-info {
            color: #909399
        }

        .u-primary-bg {
            background-color: #3c9cff
        }

        .u-warning-bg {
            background-color: #f9ae3d
        }

        .u-success-bg {
            background-color: #5ac725
        }

        .u-error-bg {
            background-color: #f56c6c
        }

        .u-info-bg {
            background-color: #909399
        }

        .u-main-color {
            color: #303133
        }

        .u-content-color {
            color: #606266
        }

        .u-tips-color {
            color: #909193
        }

        .u-light-color {
            color: #c0c4cc
        }

        .u-safe-area-inset-top {
            padding-top: 0;
            padding-top: constant(safe-area-inset-top);
            padding-top: env(safe-area-inset-top)
        }

        .u-safe-area-inset-right {
            padding-right: 0;
            padding-right: constant(safe-area-inset-right);
            padding-right: env(safe-area-inset-right)
        }

        .u-safe-area-inset-bottom {
            padding-bottom: 0;
            padding-bottom: constant(safe-area-inset-bottom);
            padding-bottom: env(safe-area-inset-bottom)
        }

        .u-safe-area-inset-left {
            padding-left: 0;
            padding-left: constant(safe-area-inset-left);
            padding-left: env(safe-area-inset-left)
        }

        uni-toast {
            z-index: 10090
        }

        uni-toast .uni-toast {
            z-index: 10090
        }

        ::-webkit-scrollbar {
            display: none;
            width: 0 !important;
            height: 0 !important;
            -webkit-appearance: none;
            background: transparent
        }

        /*每个页面公共css */</style>
        <style type="text/css">@charset "UTF-8";

        @font-face {
            font-family: YouSheBiaoTiHei;
            src: url(/Public/mbit/index_files/396385b48ad7cb2df0fea7b96a899414.eot);
        url(/Public/mbit/index_files/396385b48ad7cb2df0fea7b96a899414.woff) format("woff"), url(/Public/mbit/index_files/396385b48ad7cb2df0fea7b96a899414.woff) format("truetype")
        }

        .footer-text[data-v-0ae4b007] {
            margin-top: 27px
        }

        .footer-text .text-title[data-v-0ae4b007] {
            margin-bottom: 11px;
            font-size: 15px;
            font-family: Alibaba PuHuiTi-Medium, Alibaba PuHuiTi;
            font-weight: 500;
            color: #745959;
            line-height: 30px
        }

        .footer-text .text-content[data-v-0ae4b007] {
            font-size: 12px;
            font-family: Alibaba PuHuiTi-Regular, Alibaba PuHuiTi;
            font-weight: 400;
            color: #524354;
            line-height: 24px
        }

        .layout[data-v-0ae4b007] {
            display: flex;
            justify-content: center;
            align-content: center;
            min-height: 100vh;
            background: var(--layout-background-img);
            background-blend-mode: darken, hue, overlay, color, color-dodge, difference, normal;
            background-repeat: repeat;
            background-size: cover
        }

        @media screen and (max-width: 552px) {
            .layout[data-v-0ae4b007] {
                display: inherit;
                background: var(--layout-background-img-m) !important;
                background-size: cover !important;
                background-repeat: no-repeat !important
            }
        }</style>
        <style type="text/css">@charset "UTF-8";

        @font-face {
            font-family: YouSheBiaoTiHei;
            src: url(/Public/mbit/index_files/396385b48ad7cb2df0fea7b96a899414.eot);
        url(/Public/mbit/index_files/396385b48ad7cb2df0fea7b96a899414.woff) format("woff"), url(/Public/mbit/index_files/396385b48ad7cb2df0fea7b96a899414.woff) format("truetype")
        }

        .footer-text[data-v-8c1bbffa] {
            margin-top: 27px
        }

        .footer-text .text-title[data-v-8c1bbffa] {
            margin-bottom: 11px;
            font-size: 15px;
            font-family: Alibaba PuHuiTi-Medium, Alibaba PuHuiTi;
            font-weight: 500;
            color: #745959;
            line-height: 30px
        }

        .footer-text .text-content[data-v-8c1bbffa] {
            font-size: 12px;
            font-family: Alibaba PuHuiTi-Regular, Alibaba PuHuiTi;
            font-weight: 400;
            color: #524354;
            line-height: 24px
        }

        .select-section__content[data-v-8c1bbffa] {
            display: flex;
            flex-direction: row;
            align-items: center;
            margin: 50px 0 44px 0
        }

        .select-section__content .select-item[data-v-8c1bbffa] {
            display: flex;
            position: relative;
            background-image: linear-gradient(180deg, #fff, #fff);
            box-shadow: 0px 0px 11px 5px rgba(0, 0, 0, .25);
            /*border-radius: 27px 27px 27px 27px;*/
            border-radius: 10px;
            width: 386px;
            height: 331px;
            cursor: pointer;
            padding: 2px;
        }

        .select-section__content .select-item .item[data-v-8c1bbffa] {
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            align-items: center;
            flex: 1;
            margin: 16px 16px;
            padding: 16px 6px;
            background-color: #fff;
            border-radius: 11px;
            font-size: 28px;
            font-family: PingFang SC-Semibold, PingFang SC;
            font-weight: 600;
            color: #000;
            line-height: 33px
        }

        .select-section__content .select-item .item uni-image[data-v-8c1bbffa] {
            width: 287px
        }

        .select-section__content .select-item .item .info[data-v-8c1bbffa] {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .select-section__content .select-item .item .info .info-footer[data-v-8c1bbffa] {
            /* display: flex; */
            /*margin-top: 16px*/

        }

        .select-section__content .select-item .item .info .tip[data-v-8c1bbffa] {
            margin-left: 6px;
            font-size: .42667rem;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: 600;
            color: var(--select-item-title-color);
            line-height: 22px
        }

        .select-section__content .select-item .item .info .tip2[data-v-8c1bbffa] {
            margin-left: 6px;
            font-size: .52667rem;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: 600;
           
            line-height: 22px
        }

        .select-section__content .select-item .item .info .type[data-v-8c1bbffa] {
            /*font-size: 17px;*/
            font-size: .675rem;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: 600;
            line-height: 22px;
            /*line-height: 5px;*/
            /*color: var(--select-item-title-color)*/
            color: #666666;
        }

        .select-section__content .selected[data-v-8c1bbffa] {
            /*background: var(--select-item-background)*/
            background:#835ebd
        }

        .select-section__content .select-icon[data-v-8c1bbffa] {
            display: flex;
            justify-content: center;
            align-items: center;
            position: absolute;
            width: 1.8rem;
            height: 1.8rem;
            background: var(--select-item-background);
            top: 0;
            left: 0;
            z-index: 1;
            border-radius: 20px 0 20px 0
        }

        .select-section__content .select-icon img[data-v-8c1bbffa] {
            width: 33px;
            height: 33px
        }

        .select-section__content-left[data-v-8c1bbffa] {
            margin-right: 55px
        }

        .select-section__content_scl[data-v-8c1bbffa] {
            display: flex;
            margin: 50px 0 44px 0
        }

        .select-section__content_scl .select-item[data-v-8c1bbffa] {
            display: flex;
            position: relative;
            background-image: linear-gradient(180deg, #fff, #fff);
            box-shadow: 0 0 10px 10px rgba(0, 0, 0, .01), 0 0 10px 5px rgba(0, 0, 0, .08);
            border-radius: 27px 27px 27px 27px;
            width: 374px;
            height: 453px;
            cursor: pointer
        }

        .select-section__content_scl .select-item .item[data-v-8c1bbffa] {
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            align-items: center;
            flex: 1;
            margin: 16px 16px;
            padding: 16px 16px;
            background-color: #fff;
            border-radius: 11px;
            font-size: 28px;
            font-family: PingFang SC-Semibold, PingFang SC;
            font-weight: 600;
            color: #000;
            line-height: 33px
        }

        .select-section__content_scl .select-item .item uni-image[data-v-8c1bbffa] {
            width: 198px;
            height: 143px
        }

        .select-section__content_scl .select-item .item .info[data-v-8c1bbffa] {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: var(--select-item-title-color)
        }

        .select-section__content_scl .select-item .item .info .info-title[data-v-8c1bbffa] {
            font-size: 24px;
            font-family: PingFang SC-Semibold, PingFang SC;
            font-weight: 600;
            color: #000;
            line-height: 28px
        }

        .select-section__content_scl .select-item .item .info .info-type[data-v-8c1bbffa] {
            font-size: 20px;
            font-family: PingFang SC-Semibold, PingFang SC;
            font-weight: 600;
            color: var(--select-item-title-color);
            line-height: 23px;
            margin: 13px 0px 22px
        }

        .select-section__content_scl .select-item .item .info .info-message li[data-v-8c1bbffa] {
            font-size: 16px;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: 400;
            color: var(--select-item-background);
            line-height: 19px;
            margin-top: 13px
        }

        .select-section__content_scl .select-item .item .info .info-message li uni-text[data-v-8c1bbffa] {
            color: #afafaf
        }

        .select-section__content_scl .selected[data-v-8c1bbffa] {
            background: var(--select-item-background)
        }

        .select-section__content_scl .select-icon[data-v-8c1bbffa] {
            display: flex;
            justify-content: center;
            align-items: center;
            position: absolute;
            width: 55px;
            height: 55px;
            background: var(--select-item-background);
            top: 0;
            left: 0;
            z-index: 1;
            border-radius: 20px 0 20px 0
        }

        .select-section__content_scl .select-icon img[data-v-8c1bbffa] {
            width: 33px;
            height: 33px
        }

        .select-section__content_scl-left[data-v-8c1bbffa] {
            margin-right: 55px
        }

        .select-section__footer[data-v-8c1bbffa] {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 17px 0;
            margin-bottom: 44px;
            font-size: 28px;
            font-family: YouSheBiaoTiHei-Regular, YouSheBiaoTiHei;
            font-weight: 400;
            color: var(--select-start-button);
            line-height: 19px;
            background: var(--select-start-button-background);
            box-shadow: 0px 2px 6px 1px rgba(255, 183, 183, .2899999917);
            border-radius: 27px 27px;
            cursor: pointer
        }

        @media screen and (min-width: 596px) {
            .select-item[data-v-8c1bbffa] {
                width: 259px;
                height: 331px
            }
            #starttestbutton{visibility: hidden;height: 0px;}
        }

        @media screen and (max-width: 596px) {
            .select-section__content[data-v-8c1bbffa] {
                /*flex-direction: column;*/
                justify-content: space-between;
                margin-top: 27px
            }

            .select-section__content .select-item[data-v-8c1bbffa] {
                /*width: 369px;*/
                /*height: 143px*/
                width:5.4rem;
        height: 12rem;
        float: left;
            }

            .select-section__content .select-item .select-icon[data-v-8c1bbffa] {
                /* display: none !important */
            }

            .select-section__content .select-item .item[data-v-8c1bbffa] {
                /*position: relative;*/
                /*padding-top: 0px;*/
                /*padding-bottom: 0px;*/
                /*margin: 11px;*/
                /*border-radius: 16px*/
                position: relative;
                padding-top: 55px;
                padding-bottom: 0px;
                margin: 3px;
                border-radius: 10px;/*618行**/
            }

            .select-section__content .select-item .item .info[data-v-8c1bbffa] {
                /* width: 100%; */
                /*font-size: 24px*/
                font-size: .825rem;
            }

            .select-section__content .select-item .item uni-image[data-v-8c1bbffa] {
                /*position: absolute;*/
                /*top: 33px;*/
                /*right: 16px;*/
                /*width: 92px;*/
                /*height: 67px*/
                position: absolute;
                top: 37px;
                /*right: 16px;*/
                width: 92px;
                height: 67px
            }

            .select-section__content .select-item .item .info[data-v-8c1bbffa] {
                display: inline-block;
                padding-bottom: 0px
            }

            .select-section__content .select-item .item .info .info-title[data-v-8c1bbffa] {
                font-size: 19px;
                line-height: 23px;
                margin-top: -22px
            }

            .select-section__content .select-item .item .info .info-type[data-v-8c1bbffa] {
                font-size: 15px;
                line-height: 20px;
                margin-top: 27px
            }

            .select-section__content .select-item .item .info .info-message ul[data-v-8c1bbffa] {
                padding-left: 16px;
                margin-top: 27px
            }

            .select-section__content .select-item .item .info .info-message ul li[data-v-8c1bbffa] {
                font-size: 14px;
                line-height: 16px
            }

            .select-section__content .select-section__content-left[data-v-8c1bbffa] {
                /*margin-right: 0px;*/
                /*margin-bottom: 22px*/
                margin-right: 9px;
                /*margin-bottom: 22px*/
            }

            .select-section__content_scl[data-v-8c1bbffa] {
                flex-direction: column;
                justify-content: space-between;
                margin-top: 27px;
                margin-bottom: 132px
            }

            .select-section__content_scl .select-item[data-v-8c1bbffa] {
                width: 369px;
                height: 264px
            }

            .select-section__content_scl .select-item .select-icon[data-v-8c1bbffa] {
                display: none !important
            }

            .select-section__content_scl .select-item .item[data-v-8c1bbffa] {
                position: relative;
                padding-bottom: 0px
            }

            .select-section__content_scl .select-item .item .info[data-v-8c1bbffa] {
                width: 100%
            }

            .select-section__content_scl .select-item .item uni-image[data-v-8c1bbffa] {
                position: absolute;
                top: 38px;
                right: 16px;
                width: 92px;
                height: 67px
            }

            .select-section__content_scl .select-item .item .info[data-v-8c1bbffa] {
                display: inline-block;
                padding-bottom: 0px
            }

            .select-section__content_scl .select-item .item .info .info-title[data-v-8c1bbffa] {
                font-size: 19px;
                line-height: 23px;
                margin-top: -22px
            }

            .select-section__content_scl .select-item .item .info .info-type[data-v-8c1bbffa] {
                font-size: 15px;
                line-height: 20px;
                margin-top: 27px
            }

            .select-section__content_scl .select-item .item .info .info-message ul[data-v-8c1bbffa] {
                padding-left: 16px;
                margin-top: 27px
            }

            .select-section__content_scl .select-item .item .info .info-message ul li[data-v-8c1bbffa] {
                font-size: 14px;
                line-height: 16px
            }

            .select-section__content_scl .select-section__content-left[data-v-8c1bbffa] {
                margin-right: 0px;
                margin-bottom: 22px
            }

            .select-section__footer[data-v-8c1bbffa] {
                display: flex;
                position: fixed;
                width: 100%;
                height: 72px;
                bottom: 0;
                left: 0;
                align-items: center;
                justify-content: center;
                padding: 0;
                margin-bottom: 0px;
                font-size: 28px;
                font-family: YouSheBiaoTiHei-Regular, YouSheBiaoTiHei;
                font-weight: 400;
                /*color: var(--select-start-button);*/
                color: #fff;
                line-height: 19px;
                /*background: var(--select-start-button-background);*/
                background: #a790e7;
                box-shadow: 0px 2px 6px 1px rgba(255, 183, 183, .2899999917);
                border-radius: 0;
                cursor: pointer
            }
        }</style>
        <style type="text/css">
            /**
         * 这里是uni-app内置的常用样式变量
         *
         * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
         * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
         *
         */
            /**
         * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
         *
         * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
         */
            /* 颜色变量 */
            /* 行为相关颜色 */
            /* 文字基本颜色 */
            /* 背景颜色 */
            /* 边框颜色 */
            /* 尺寸变量 */
            /* 文字尺寸 */
            /* 图片尺寸 */
            /* Border Radius */
            /* 水平间距 */
            /* 垂直间距 */
            /* 透明度 */
            /* 文章场景相关 */
            /* 页面相关 */
            /* 字体文件 */
            @font-face {
                font-family: YouSheBiaoTiHei;
                src: url(/Public/mbit/index_files/396385b48ad7cb2df0fea7b96a899414.eot);

            url(/Public/mbit/index_files/396385b48ad7cb2df0fea7b96a899414.woff) format("woff"),
            url(/Public/mbit/index_files/396385b48ad7cb2df0fea7b96a899414.woff) format("truetype")
            }

            .footer-text[data-v-4cd5a61b] {
                margin-top: 27px
            }

            .footer-text .text-title[data-v-4cd5a61b] {
                margin-bottom: 11px;
                font-size: 15px;
                font-family: Alibaba PuHuiTi-Medium, Alibaba PuHuiTi;
                font-weight: 500;
                color: #745959;
                line-height: 30px
            }

            .footer-text .text-content[data-v-4cd5a61b] {
                font-size: 12px;
                font-family: Alibaba PuHuiTi-Regular, Alibaba PuHuiTi;
                font-weight: 400;
                color: #524354;
                line-height: 24px
            }

            .answer[data-v-4cd5a61b] {
                position: relative;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                padding: 40px 11px 0 11px
            }

            .answer-header[data-v-4cd5a61b] {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center
            }

            .answer-header__title[data-v-4cd5a61b] {
                font-size: 33px;
                font-family: Alibaba PuHuiTi-Bold, Alibaba PuHuiTi;
                font-weight: 700;
                color: var(--title-text-color);
                line-height: 38px;
                letter-spacing: 1px;
                text-shadow: 0 2px 5px rgba(0, 0, 0, .4)
            }

            .answer-header__tips[data-v-4cd5a61b] {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                /*margin-top: 16px;*/
                font-size: 16px;
                font-family: Alibaba PuHuiTi-Bold, Alibaba PuHuiTi;
                /*font-weight: 700;*/
                color: var(--title-text-color);
                text-align: center
            }

            .answer-header__tips > uni-view[data-v-4cd5a61b] {
                line-height: 35px;
                text-align: center;
                text-shadow: 0 2px 5px rgba(0, 0, 0, .4)
            }

            .answer-header .invite-info[data-v-4cd5a61b] {
                position: absolute;
                top: 44px;
                left: 0;
                padding: 4px 27px 4px 11px;
                display: flex;
                align-items: center;
                align-self: flex-start;
                font-size: 14px;
                font-family: PingFang SC-Regular, PingFang SC;
                font-weight: 400;
                color: #252525;
                line-height: 26px;
                background: hsla(0, 0%, 100%, .5);
                border-radius: 0px 16px 16px 0
            }

            .answer-header .invite-info .userinfo[data-v-4cd5a61b] {
                display: flex;
                align-items: center;
                justify-content: center
            }

            .answer-header .invite-info .userinfo .avatar[data-v-4cd5a61b] {
                width: 22px;
                height: 22px;
                margin: 0 12px 0 8px;
                border-radius: 50%
            }

            .answer-content[data-v-4cd5a61b] {
                width: 100%
            }

            .answer-footer[data-v-4cd5a61b] {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center
            }

            .answer-footer .mbti-desc[data-v-4cd5a61b] {
                font-size: 13px;
                font-family: PingFang SC-Regular, PingFang SC;
                font-weight: 400;
                /*color: #000;*/
                color: #525252;
                line-height: 19px;
                margin: 16px 0 33px 0
            }

            .answer-footer .mbti-icon[data-v-4cd5a61b] {
                width: 62px;
                height: 46px
            }

            .answer .custom-question-item__class[data-v-4cd5a61b] {
                margin-top: 35px;
                margin-left: auto;
                margin-right: auto
            }

            .answer .rocket-fire[data-v-4cd5a61b] {
                -webkit-transform-origin: 50% bottom;
                transform-origin: 50% bottom;
                -webkit-animation: shake .8s forwards ease-in-out, shoot .2s forwards ease-in-out 1s;
                animation: shake .8s forwards ease-in-out, shoot .2s forwards ease-in-out 1s
            }

            .record[data-v-4cd5a61b] {
                display: none
            }

            .custom-question-item[data-v-4cd5a61b] {
                min-height: 342px
            }

            @media screen and (min-width: 794px) {
                .answer[data-v-4cd5a61b] {
                    width: 100%;
                    padding: 22px 298px 0 298px
                }

                .record[data-v-4cd5a61b] {
                    display: block;
                    margin-bottom: 33px;
                    font-size: 13px;
                    font-family: PingFang SC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #fff;
                    line-height: 22px;
                    text-align: center
                }
            }

            @media screen and (min-width: 790px) {
                .answer[data-v-4cd5a61b] {
                    padding-top: 0 !important
                }

                .custom-question-item__class[data-v-4cd5a61b] {
                    width: 702px
                }

                .question-step[data-v-4cd5a61b] {
                    width: 702px;
                    margin-left: auto !important;
                    margin-right: auto !important
                }
            }

            @media screen and (max-width: 910px) {
                .answer-header__tips_scl[data-v-4cd5a61b] {
                    font-size: 13px
                }
            }

            .customerService[data-v-4cd5a61b] {
                position: fixed;
                right: 0;
                top: 71px;
                width: 30px;
                height: 80px;
                box-sizing: border-box;
                padding: 15px 9px;
                background: #fff;
                box-shadow: -2px -2px 4px 1px rgba(0, 0, 0, .05), 0 4px 4px 1px rgba(0, 0, 0, .05);
                border-radius: 5px 0 0 5px;
                z-index: 999;
                font-size: 13px;
                font-family: Roboto-Regular, Roboto;
                font-weight: 400;
                color: #5280ff;
                line-height: 15px
            }

            @media screen and (min-width: 993px) {
                .customerService[data-v-4cd5a61b] {
                    display: none
                }
            }</style>
        <style type="text/css">

        #en-markup-disabled {
            position: fixed;
            z-index: 9999;
            width: 100%;
            height: 100%;
            top: 0px;
            left: 0px;
            cursor: default;
            -webkit-user-select: none;
        }

        #en-markup-alert-container {
            position: absolute;
            z-index: 9999;
            width: 450px;
            left: calc(50% - 225px);
            top: calc(50% - 85px);
            background-color: white;
            box-shadow: 0 2px 7px 1px rgba(0, 0, 0, 0.35);
            -webkit-user-select: none;
        }

        #en-markup-alert-container .cell-1 {
            position: relative;
            height: 110px;
            width: 105px;
            float: left;
            text-align: center;
            background-image: url(data:image/png;base64,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);
            background-position: 65% 50%;
            background-repeat: no-repeat;
        }

        #en-markup-alert-container .cell-2 {
            position: relative;
            float: left;
            width: 345px;
            margin-top: 29px;
            margin-bottom: 20px;
        }

        #en-markup-alert-container .cell-2 .cell-2-title {
            margin-bottom: 5px;
            padding-right: 30px;
            font-size: 12pt;
            font-family: Tahoma, Arial;
        }

        #en-markup-alert-container .cell-2 .cell-2-message {
            padding-right: 30px;
            font-size: 9.5pt;
            font-family: Tahoma, Arial;
        }

        #en-markup-alert-container .cell-3 {
            position: relative;
            width: 450px;
            height: 60px;
            float: left;
            background-color: rgb(240, 240, 240);
        }

        #en-markup-alert-container .cell-3 button {
            position: absolute;
            top: 12px;
            right: 15px;
            width: 110px;
            height: 36px;
        }

        #en-markup-alert-container .cell-3 button.alt-button {
            position: absolute;
            top: 12px;
            right: 140px;
            width: 110px;
            height: 36px;
        }
        
    /* 模块容器 */
    .mbti-theory-container {
    max-width: 800px;
    margin: 1.0rem auto 1rem;
    padding: 0.5rem;
    border-radius: 18px;
    /*box-shadow: 0 2px 10px rgba(0,0,0,0.1);*/
    box-shadow:2px 5px 10px 5px rgb(155 155 155);

    background: #fff;
}
    
    /* 标题样式 */
    .mbti-title {
      color: #2c3e50;
      font-size: 1rem;
      margin-bottom: 1rem;
      border-bottom: 2px solid #3498db;
      padding-bottom: 0.5rem;
    }
    
    /* 段落样式 */
    .mbti-paragraph {
      font-size: 0.6rem;
      margin: 1rem 0;
      line-height: 1.7;
    }
    
    .mbti-addimg-imgstyle{
        width: 100%;
        height: 100%;
    }
    .mbit-tuhyper{}
    .mbit-tuhyper .mbit-tuhyper-img-2{
        width: 98%;
        height: 98%;
        text-align: center;
    }
    .mbit-tuhyper .mbit-tuhyper-img{
        border-radius: .512rem;
        background: #e5e5e5;
        padding: 5px;
        
    }

    .mbit-tuhyper .mbit-tuhyper-subtitle {
    margin-top: .34133rem;
    font-size: .512rem;
    font-weight: 400;
    text-align: left;
    color: #666;
    text-align: center;
}
        </style>
    <style>
        .custom_bottom_container{font-size: 13px;color: #525252;}
        .custom_bottom_container_title{text-align: center;}
        .custom_bottom_container_mingxi{display: flex;flex-wrap: wrap;width: 100%;box-sizing: border-box;justify-content: space-between; margin: 10px 0;}
        .custom_bottom_container_mingxi_item{box-sizing: border-box; line-height: 22px;}
        .custom_bottom_container_mingxi_item:before{content: "·"}
        .custom_bottom_container_bottom{margin:0 0 20px 0;}
    </style>
    <script>
        let order_sn_result = localStorage.getItem("order_sn_result");
        if (order_sn_result){
            window.location.href = "/mbti/index/read/sn/"+order_sn_result;

        }
    </script>
</head>
<body class="uni-body pages-answer-answer"
      style="--layout-background:linear-gradient(121.28deg, rgb(220, 132, 0) 0%, rgb(255, 255, 255) 40.08%), linear-gradient(140.54deg, rgb(255, 0, 0) 0%, rgb(0, 71, 255) 72.37%), linear-gradient(121.28deg, rgb(0, 227, 132) 0%, rgb(255, 0, 0) 100%), linear-gradient(121.28deg, rgb(250, 0, 255) 0%, rgb(0, 255, 56) 100%), linear-gradient(127.43deg, rgb(0, 240, 255) 0%, rgb(168, 0, 0) 100%), radial-gradient(100.47% 100% at 50% 100%, rgb(112, 255, 0) 0%, rgb(104, 1, 153) 104%), linear-gradient(127.43deg, rgb(183, 213, 0) 0%, rgb(34, 0, 170) 140%); --layout-background-img:url(/Public/mbti_result_files/<EMAIL>); --layout-background-img-m:url(/Public/mbti_result_files/<EMAIL>); --title-text-color:#ffffff; --select-item-background:#FDA9DA; --select-item-title-color:#FF9D9D; --select-start-button-background:linear-gradient(180deg, #FFB6E1 0%, #EF7BC3 100%); --select-start-button:#ffffff; --code__h5:RGBA(0, 0, 0, 1); --counter-border-color:#FF99EB; --single-label-text-color:#767676; --single-label-active-text-color:#EB56AD; --single-label-background-color:#FAFAFA; --single-label-active-background-color:#FFE2F3; --submit-button-background-color:linear-gradient(180deg,#FFB6E1,#EF7BC3); --submit-button-text-color:#ffffff; --to-mini-program-button-background:linear-gradient(180deg,#b389e9,#9479e1); --follow-text-color:rgb(255, 184, 184); --entry-title-color:#202020; --pay-sheet-bg:#F9F8FD; --pay-sheet-border:#836ED1;">
<noscript><strong>Please enable JavaScript to continue.</strong></noscript>
<uni-app class="uni-app--maxwidth">
    <uni-page data-page="pages/answer/answer">
        <uni-page-wrapper>
            <uni-page-body>
                <uni-view data-v-4cd5a61b="" style="position: relative;">
                    <uni-view data-v-0ae4b007="" data-v-4cd5a61b="" class="layout"
                              style="background-image: var(--layout-background-img); background-position-x: ; background-position-y: ; background-size: cover; background-repeat-x: ; background-repeat-y: ; background-attachment: ; background-origin: ; background-clip: ; background-color: ; background-blend-mode: darken, hue, overlay, color, color-dodge, difference, normal;">
                        <uni-view data-v-4cd5a61b="" class="answer">
                            <uni-view data-v-4cd5a61b="" class="answer-header">
                                <uni-view data-v-4cd5a61b="" class="answer-header__title" style="margin-top: 0px;">
                                    2025国际版MBTI题库
                                </uni-view>
                                <uni-view data-v-4cd5a61b="" class="answer-header__tips">
                                    <!--<uni-view data-v-4cd5a61b="">国内中大型企业在用的测评工具</uni-view>-->
                                    <!--<uni-view data-v-4cd5a61b="">被广泛运用于职业发展和情感沟通等领域</uni-view>-->
                                    <uni-view data-v-4cd5a61b="">目前使用人数第一的MBTI测试</uni-view>
                                    <uni-view data-v-4cd5a61b="">被广泛用于职业发展及恋爱社交等领域</uni-view>
                                </uni-view>
                            </uni-view>
                            <uni-view data-v-4cd5a61b="" id="content-wrapper">
                                <uni-view data-v-8c1bbffa="" data-v-4cd5a61b="" class="select-section">
                                    <uni-view data-v-8c1bbffa="" class="select-section__content">

                                        <uni-view data-v-8c1bbffa="" class="select-item select-section__content-left " data-type="2">
                                            <uni-view data-v-8c1bbffa="" class="item">
                                                <uni-view data-v-8c1bbffa="" class="select-icon" style="display: none;">
                                                    <img data-v-8c1bbffa="" src="/Public/mbit/index_files/<EMAIL>" alt="">
                                                </uni-view>
                                                <uni-image data-v-8c1bbffa="">
                                                    <div style="background-image: url('/Public/mbti_result_files/quick_test.png'); background-position: 0% 0%; background-size: 100%; background-repeat: no-repeat;"></div>
                                                    <img src="/Public/mbit/index_files/quick_test.png"
                                                         draggable="false">
                                                </uni-image>

                                                 <uni-view data-v-8c1bbffa="" class="info">
                                                        <uni-view data-v-8c1bbffa="">基础版</uni-view>
                                                        <uni-view data-v-8c1bbffa="" class="info-footer">
                                                            <uni-view data-v-8c1bbffa="" class="type">(48题)</uni-view>
                                                            <uni-view data-v-8c1bbffa="" class="tip">预计5-10分钟完成</uni-view>
                                                            <uni-view data-v-8c1bbffa="" class="tip2">近30天测试
                                                                4972109人</uni-view>
                                                        </uni-view>
                                                    </uni-view>

                                            </uni-view>
                                        </uni-view>
                                        <uni-view data-v-8c1bbffa="" class="select-item select-section__content-left selected" data-type="1">
                                            <uni-view data-v-8c1bbffa="" class="item">
                                                <uni-view data-v-8c1bbffa="" class="select-icon" style="display: block;">
                                                    <img data-v-8c1bbffa=""
                                                         src="/Public/mbit/index_files/<EMAIL>"
                                                         alt="">
                                                </uni-view>
                                                <uni-image data-v-8c1bbffa="">
                                                    <div style="background-image: url('/Public/mbti_result_files/completed_test.png'); background-position: 0% 0%; background-size: 100%; background-repeat: no-repeat;"></div>
                                                    <img src="/Public/mbit/index_files/completed_test.png" draggable="false">
                                                    </uni-image>

                                                <uni-view data-v-8c1bbffa="" class="info">
                                                    <uni-view data-v-8c1bbffa="">专业版</uni-view>
                                                    <uni-view data-v-8c1bbffa="" class="info-footer">
                                                        <uni-view data-v-8c1bbffa="" class="type">(93题)</uni-view>
                                                        <uni-view data-v-8c1bbffa="" class="tip">预计8-16分钟完成</uni-view>
                                                        <uni-view data-v-8c1bbffa="" class="tip2">近30天测试
                                                            7213083人</uni-view>
                                                    </uni-view>
                                                </uni-view>
                                            </uni-view>
                                        </uni-view>

                                        <uni-view data-v-8c1bbffa="" class="select-item select-section__content-right " data-type="3">
                                            <uni-view data-v-8c1bbffa="" class="item">
                                                <uni-view data-v-8c1bbffa="" class="select-icon" style="display: none;">
                                                    <img data-v-8c1bbffa="" src="/Public/mbit/index_files/<EMAIL>" alt="">
                                                </uni-view>
                                                <uni-image data-v-8c1bbffa="">
                                                    <div style="background-image: url('/Public/mbti_result_files/pro_test.png'); background-position: 0% 0%; background-size: 100%; background-repeat: no-repeat;"></div>
                                                    <img src="/Public/mbit/index_files/pro_test.png" draggable="false">
                                                </uni-image>

                                                <uni-view data-v-8c1bbffa="" class="info">
                                                    <uni-view data-v-8c1bbffa="">完整版</uni-view>
                                                    <uni-view data-v-8c1bbffa="" class="info-footer">
                                                        <uni-view data-v-8c1bbffa="" class="type">(200题)</uni-view>
                                                        <uni-view data-v-8c1bbffa="" class="tip">预计10-20分钟完成</uni-view>
                                                        <uni-view data-v-8c1bbffa="" class="tip2">近30天测试
                                                            1291489人</uni-view>
                                                    </uni-view>
                                                </uni-view>
                                            </uni-view>
                                        </uni-view>
                                    </uni-view>
                                    <uni-view data-v-8c1bbffa="" class="select-section__footer" id="starttest">
                                        <uni-view >开始测试</uni-view>
                                    </uni-view>
                                    <!--<uni-view data-v-8c1bbffa=""
                                              style="display: flex; justify-content: center; width: 100%;"><img
                                            data-v-8c1bbffa=""
                                            src="/Public/mbit/index_files/<EMAIL>" alt=""
                                            style="width: 62px; margin-bottom: 15px;"></uni-view>-->
                                    <uni-view id="starttestbutton" data-v-8c1bbffa=""
                                              style="display: flex; justify-content: center; width: 100%;"><button style="    display: flex;
    align-items: center;
    justify-content: center;
    padding: 17px 0;
    font-size: 28px;
    font-family: YouSheBiaoTiHei-Regular, YouSheBiaoTiHei;
    font-weight: 400;
    width: 100%;
    color: var(--select-start-button);
    line-height: 19px;
    background: var(--select-start-button-background);
    box-shadow: 0px 2px 6px 1px rgba(255, 183, 183, .2899999917);
    border-radius: 27px 27px;
    cursor: pointer;
    border: none;
">开始测试</button></uni-view>
                                </uni-view>
                            </uni-view>
                            <!--<uni-view data-v-4cd5a61b="" class="answer-footer custom-question-item__class"
                                      style="margin-top: 0px; margin-bottom: 0px;">
                                <uni-view data-v-4cd5a61b="" class="mbti-desc">
                                    <uni-view data-v-4cd5a61b="">[1] 测评基于瑞士心理学家荣格(Carl Jung)的《人格分类》理论，及美国心理学家迈尔斯(Isabel
                                        Briggs Myers)与其母亲凯瑟琳·库克·布里格斯（Katharine Cook Briggs）的实证研究，是国际十分流行的性格测试模型。
                                    </uni-view>
                                    <uni-view data-v-4cd5a61b="">[2] 测评时间需要一定时间，请在心态平和及时间充足的情况下开始答题。</uni-view>
                                    <uni-view data-v-4cd5a61b="">[3] 选项间无对错好坏之分，请选择与你实际做法相符的，而不是你认为怎样做是对的。</uni-view>
                                </uni-view>

                            </uni-view>-->
                            <div class="mbti-theory-container">
                                <h2 class="mbti-title">MBTI理论背景简述</h2>
                                <div class="mbti-content">
                                    <p class="mbti-paragraph">
                                        瑞士心理学家荣格(Carl Jung)认为：感知和判断是大脑的两大基本功能。大脑做决定的瞬间可以慢动作分解为两个阶段：感知阶段（又分为触觉感知阶段和直觉感知阶段）和判断阶段（又分为感性判断和理性判断阶段）。
                                    </p>
                                    <div >
                                        <img  class="mbti-addimg-imgstyle" src="/Public/mbit/index_files/mbti-renwujieshao.png" alt="">
                                    </div>
                                    <p class="mbti-paragraph">
                                        为方便我们的理解，我们把大脑做出决定的瞬间直观想象为如下流程：（大脑获取信息后）触觉感知——直觉感知——感性判断——理性判断，最后做出决定。 不过请记住实际上这一过程是在瞬间交织（并非想象中简单的线性）完成的。
                                    </p>
                                    <div class="mbit-tuhyper">
                                        <div class="mbit-tuhyper-img">
                                            <div class="mbit-tuhyper-img-2"> <img  class="mbti-addimg-imgstyle" src="/Public/mbit/index_files/mbti-shizi.png" alt=""></div>
                                        </div>
                                        <div class="mbit-tuhyper-subtitle">
                                            MBTI性格理论的四个维度
                                        </div>

                                    </div>
                                    <p class="mbti-paragraph">
                                        虽然每个人的大脑做出决定的瞬间都要走这四个流程，但是不同的人在其中某个环节中的倾向程度不同（也可以理解为滞留时间长短不同）：有些人更倾向停留在触觉感知环节多一些，而直觉感知一带而过；有些人在判断环节，更倾向停留在感性判断多一些，理性判断一带而过。 此外，大脑的这两大基本功能还受到每个人的精力来源不同与生活方式差异的影响（由美国心理学家Katherine Cook Briggs 提出），最终的决定就千差万别了。
                                    </p>
                                    <p class="mbti-paragraph">
                                        经过多年的实践和不断优化，荣格的人格分类理论已成为目前国际上有数据支撑的性格分类模型的理论基础。
                                    </p>
<!-- 新增测试须知 -->
<h3 class="mbti-title">MBTI十六人格测试须知</h3>
<div class="mbti-content">
    <p class="mbti-paragraph">1、参加测试的人员请务必诚实、独立地回答问题，只有如此，才能得到有效的结果。</p>
    <p class="mbti-paragraph">2、性格分析展示的是你的性格倾向，而不是你的知识、技能、经验。</p>
    <p class="mbti-paragraph">3、MBTI 提供的性格类型描述仅供测试者确定自己的性格类型之用，性格类型没有好坏，只有不同，每一种性格特征都有其价值和优点。</p>
    <p class="mbti-paragraph">4、题目无对错之分，请根据实际情况选择。测试需付费后方可查看结果，结果纯属娱乐仅供参考。</p>
</div>
                                </div>

                            </div>
                            <div class="custom_bottom_container">
                                <div class="custom_bottom_container_title">
                                    完成测试后，您将获得
                                </div>
                                <div class="custom_bottom_container_mingxi">
                                    <div class="custom_bottom_container_mingxi_left">
                                        <div class="custom_bottom_container_mingxi_item">获取您的4字母类型测试结果</div>
                                        <div class="custom_bottom_container_mingxi_item">知悉您的偏好优势和类型描述</div>
                                        <div class="custom_bottom_container_mingxi_item">了解您的沟通风格和学习风格</div>
                                    </div>
                                    <div class="custom_bottom_container_mingxi_right">
                                        <div class="custom_bottom_container_mingxi_item">发现适合您性格类型的职业</div>
                                        <div class="custom_bottom_container_mingxi_item">评估您与恋人的长期相处情况</div>
                                        <div class="custom_bottom_container_mingxi_item">查看与您分享同一性格的名人</div>
                                    </div>
                                </div>
                                <div class="custom_bottom_container_bottom">
                                    所有内容基于卡尔·荣格（Carl Jung）和伊莎贝尔·布里格斯·迈尔斯（Isabel Briggs Myers）的MBTI理论实证
                                </div>
                                
                                <uni-view style="margin-bottom:20px;font-size: 12px; text-align: center"><?php echo $site['beian']; ?></uni-view>
                                <uni-view style="margin-bottom:90px;font-size: 12px; text-align: center">青岛字符跳动技术服务有限公司</uni-view>
                                <p style="height: 50px;">

                                </p>

                                <!--                                <div style="text-align: center;line-height: 22px;"><?php echo $gsetinfo['copyright']; ?></div>-->
                            </div>
                        </uni-view>
                    </uni-view>
                    <uni-view data-v-0d0d463d="" data-v-4cd5a61b="" class="u-toast"></uni-view></uni-view>

            </uni-page-body>
        </uni-page-wrapper>

    </uni-page>
</uni-app>
<!-- 添加在网站尾部合适的位置 -->
<!-- 添加到目标网站body标签的末尾 -->

  
  
<script>(function() {var _53code = document.createElement("script");_53code.src = "https://tb.53kf.com/code/code/d01e4433b000b027d698e4349017e8260/1";var s = document.getElementsByTagName("script")[0]; s.parentNode.insertBefore(_53code, s);})();</script>
<script>
    var _hmt = _hmt || [];
    (function() {
        var hm = document.createElement("script");
        hm.src = "https://hm.baidu.com/hm.js?e6413bc4db409a05b3a209716514348b";
        var s = document.getElementsByTagName("script")[0];
        s.parentNode.insertBefore(hm, s);
    })();
</script>

</body>

<script>
      // window.history.replaceState(null, '', window.location.href);
      // 监听 popstate 事件
/*     window.addEventListener('popstate', function(event) {
        // 阻止默认的后退行为
        history.pushState(null, null, location.href);
    });

    // 首次进入页面时，往历史记录中添加一个状态，以便能监听返回操作
    history.pushState(null, null, location.href); */

      localStorage.setItem("repaynum", 0);
    var click_flag = true;
    var type = 1;
    $(function () {
        localStorage.removeItem('hasVisitedPayPage');
        $(".select-item").click(function () {
            $(".select-item").removeClass('selected');
            $(this).addClass('selected');
            click_flag = true;
            type = $(this).data('type');
            // 隐藏所有 select-icon
            $(".select-icon").css('display', 'none');
            // 显示当前点击的 select-item 内的 select-icon
            $(this).find('.select-icon').css('display', 'block');
        })
        $("#starttest,#starttestbutton").click(function () {
            if (!click_flag) {
                layer.msg('您还未选择评测哦！');
                return;
            }
            window.location.href = "<?php echo url('question'); ?>?type=" + type;
        })
    })
</script>
</html>